'use client';
import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import loadDynamic from 'next/dynamic';
import AppHeader from '../../../components/dashboard/AppHeader';
import Spinner from '~/core/ui/Spinner';
import { PageBody } from '~/core/ui/Page';
import CardButton from '~/core/ui/CardButton';
import { toast } from 'sonner';
import { InformationIcon } from '../../svgs';
import TeamTimeSheetListing from '../../../components/time-sheets/TeamTimeSheetList';
import Heading from '~/core/ui/Heading';
import TimeSheetListing from '../../../components/time-sheets/TimeSheetList';
import AnnouncementsCard from '~/components/dashboard/AnnouncementsCard';
import { useLazyGetDashboardDataQuery } from '../../_utils/redux/slice/emptySplitApi';
import { convertTo12HourFormat, formatDate } from '../../../common/common';
import Link from 'next/link';
import { commonText, dashBoardText } from '~/common/constant';
import { routes } from '~/common/routes';
import Tile from '~/core/ui/Tile';
import dayjs from 'dayjs';
import CheckInModal from '~/core/ui/components/CheckInModal';

type ShiftTimes = {
  startTime: string | null;
  endTime: string | null;
  paidLateCounts?: number;
};

type AttendanceTimes = {
  checkIn: string | null;
  checkOut: string | null;
  date: string | null;
};

type ApprovedHours = {
  approved: number;
};

type DashboardData = {
  shiftTimes?: ShiftTimes;
  projectCount?: number;
  lateCounts?: number;
  leavesCount?: number;
  workingHours?: { totalHours: number };
  attendanceTimes?: AttendanceTimes;
  isTimeSheetExist?: boolean;
  isAllManagerTimeSheetsReviewed?: boolean;
  timeSheetDate?: string;
  requiredWorkingHours?: number;
  approvedHours?: ApprovedHours;
};

const DashboardData = loadDynamic(() => import('../../../components/dashboard/DashboardDemo'), {
  ssr: false,
  loading: () => (
    <div className="flex flex-1 items-center min-h-full justify-center flex-col space-y-4">
      <Spinner className="text-primary" />
      <div>{commonText.loading}</div>
    </div>
  ),
});

function DashboardPage() {
  const { data: session, status } = useSession();
  // console.log('session', session)
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isNetworkError, setIsNetworkError] = useState(false);
  // const [isOnline, setIsOnline] = useState(true);
console.log("Authstatus", status)

  // Network connectivity detection
  // useEffect(() => {
  //   const handleOnline = () => {
  //     setIsOnline(true);
  //     // Retry fetching data when network comes back online
  //     if (error && isNetworkError && status === 'authenticated' && session?.user) {
  //       setLoading(true);
  //       setError(null);
  //       setIsNetworkError(false);
  //     }
  //   };
  //   const handleOffline = () => setIsOnline(false);

  //   setIsOnline(navigator.onLine);
  //   window.addEventListener('online', handleOnline);
  //   window.addEventListener('offline', handleOffline);

  //   return () => {
  //     window.removeEventListener('online', handleOnline);
  //     window.removeEventListener('offline', handleOffline);
  //   };
  // }, [error, isNetworkError, status, session]);
  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/dashboard?userID=${session.user.id}`, {
        cache: 'no-store',
      })
        .then((res) => res.json())
        .then((data) => {
          setDashboardData(data.content);
          setLoading(false);
          setError(null);
          setIsNetworkError(false);
        })
        .catch((err) => {
          console.error('Error fetching dashboard data:', err);
          setError(err);
          setLoading(false);

          // Check if it's a network error
          const isNetworkIssue = !navigator.onLine ||
                                err.name === 'TypeError' ||
                                err.message?.includes('fetch') ||
                                err.message?.includes('network') ||
                                err.message?.includes('Failed to fetch');
          setIsNetworkError(isNetworkIssue);
        });
    }
  }, [session, status]);

  if (status === 'loading' || loading) {
    return (
      <div className="flex flex-1 items-center min-h-full justify-center flex-col space-y-4">
        <Spinner className="text-primary" />
        <div>{commonText.loading}</div>
      </div>
    );
  }

  if (!session || !session.user) {
    return <div>{dashBoardText.unauthorized}</div>;
  }

  // Show centered "Internet issue" message when network is not connected
  if (error && (isNetworkError)) {
    return (
      <>
        <AppHeader
          title={commonText.dashboardTabLabel}
          description={commonText.dashboardTabDescription}
        />
        <PageBody>
          <div className="flex flex-1 items-center min-h-[50vh] justify-center">
            <div className="text-center">
              <Heading type={2} className="text-gray-600">
                {dashBoardText.internetIssue}
              </Heading>
              <p className="text-gray-500 mt-2">
                {dashBoardText.internetIssueDescription}
              </p>
            </div>
          </div>
        </PageBody>
      </>
    );
  }

  if (error) {
    return <div>Error loading dashboard data.</div>;
  }

  // Don't render dashboard cards if there's no data (could be due to network issues)
  if (!dashboardData) {
    return (
      <>
        <AppHeader
          title={commonText.dashboardTabLabel}
          description={commonText.dashboardTabDescription}
        />
        <PageBody>
          <div className="flex flex-1 items-center min-h-[50vh] justify-center">
            <div className="text-center">
              <Heading type={2} className="text-gray-600">
                {dashBoardText.noDataAvailable}
              </Heading>
              <p className="text-gray-500 mt-2">
                {dashBoardText.noDataDescription}
              </p>
            </div>
          </div>
        </PageBody>
      </>
    );
  }

  const {
    shiftTimes,
    projectCount,
    lateCounts,
    leavesCount,
    workingHours,
    attendanceTimes,
    isTimeSheetExist,
    isAllManagerTimeSheetsReviewed,
    timeSheetDate,
    requiredWorkingHours,
    approvedTimesheetsTime,
  } = dashboardData;

  const yesterdayDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD');

  return (
    <>
      <AppHeader
        title={commonText.dashboardTabLabel}
        description={commonText.dashboardTabDescription}
      />

      <PageBody>
        {!isTimeSheetExist && session.user.role !== 'admin' && (projectCount ?? 0) > 0 && (
          <div className="flex bg-[#FF4646] text-white py-3 px-3 mb-2 font-bold">
            <InformationIcon />
            <p className="ml-2 mr-2 text-white">
              Your timesheet for {formatDate(timeSheetDate)} is missing.
            </p>
            <Link href={routes.timeSheets}>
              <u>{dashBoardText.fillYourTimesheet}</u>
            </Link>
          </div>
        )}

        {!isAllManagerTimeSheetsReviewed && session?.user.role === 'manager' && (
          <div className="flex bg-[#FF4646] text-white py-3 px-3 mb-2 font-bold">
            <InformationIcon />
            <p className="ml-2 mr-2 text-white">
              Timesheets review are pending.
            </p>
            <Link href={routes.teamTimeSheet}><u>{dashBoardText.pleaseReview}</u></Link>
          </div>
        )}

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 mt-10 bg-white p-4">
          <Tile>
            <Tile.Header>
              <Tile.Heading>{dashBoardText.currentShiftTime}</Tile.Heading>
            </Tile.Header>
            <Tile.Body>
              <Tile.Figure figureClassName="!text-xl">
                {shiftTimes?.startTime ? convertTo12HourFormat(shiftTimes.startTime) : 'N/A'} -{' '}
                {shiftTimes?.endTime ? convertTo12HourFormat(shiftTimes.endTime) : 'N/A'}
              </Tile.Figure>
            </Tile.Body>

          </Tile>

          {session.user.role !== 'admin' && (
            <Tile>
              <div className="flex justify-between items-center space-x-2">
                <Tile.Header>
                  <Tile.Heading>{dashBoardText.checkIn}:</Tile.Heading>
                </Tile.Header>
                <Tile.Body>
                  <Tile.Figure figureClassName="!p-0 !text-xl">
                    {attendanceTimes?.checkIn ? convertTo12HourFormat(attendanceTimes.checkIn) : '-'}
                  </Tile.Figure>
                </Tile.Body>
              </div>

              <div className="flex justify-between items-center space-x-2">
                <Tile.Header>
                  <Tile.Heading>{dashBoardText.checkOut}:</Tile.Heading>
                </Tile.Header>
                <Tile.Body>
                  <Tile.Figure figureClassName="!p-0 !text-xl">
                    {attendanceTimes?.checkOut &&
                    attendanceTimes?.checkIn !== attendanceTimes?.checkOut
                      ? convertTo12HourFormat(attendanceTimes.checkOut)
                      : '-'}
                  </Tile.Figure>
                </Tile.Body>

              </div>

              <div className='w-full text-right min-w-0'>
                {attendanceTimes && attendanceTimes?.date !== null && (
                  <Tile.Trend trend="none">{attendanceTimes?.date === yesterdayDate ? dashBoardText.yesterday : formatDate(attendanceTimes?.date)}</Tile.Trend>
                )}
              </div>
            </Tile>
          )}

          <Tile>
            <Tile.Header>
              <Tile.Heading>{dashBoardText.concurrentProjects}</Tile.Heading>
            </Tile.Header>
            <Tile.Body>
              <Tile.Figure figureClassName="!text-2xl">{String(projectCount).padStart(2, '0') || '00'}</Tile.Figure>
            </Tile.Body>
          </Tile>

          <Tile>
            <Tile.Header>
              <Tile.Heading>{dashBoardText.lateCounts}</Tile.Heading>
            </Tile.Header>
            <Tile.Body>
            {/* ${(lateCounts ?? 0) > (shiftTimes?.paidLateCounts ?? 0) ? 'text-red-500' : 'text-[#003FBC]'} */}
              <Tile.Figure figureClassName={`font-bold !text-2xl`}>
                {String(lateCounts).padStart(2, '0') || '00'}
              </Tile.Figure>
            </Tile.Body>
          </Tile>

          <Tile>
            <Tile.Header>
              <Tile.Heading>{dashBoardText.leaves}</Tile.Heading>
            </Tile.Header>
            <Tile.Body>
              <Tile.Figure figureClassName="!text-2xl">{String(leavesCount).padStart(2, '0') || '00'}</Tile.Figure>
            </Tile.Body>
          </Tile>

          <Tile>
            <Tile.Header>
              <Tile.Heading>{dashBoardText.workingHours}</Tile.Heading>
            </Tile.Header>
            <Tile.Body>
              <Tile.Figure figureClassName="!text-2xl">
                {workingHours?.totalHours || '00'} / {requiredWorkingHours}
              </Tile.Figure>
            </Tile.Body>
          </Tile>

          <Tile>
            <Tile.Header>
              <Tile.Heading>{dashBoardText.approvedHours}</Tile.Heading>
            </Tile.Header>
            <Tile.Body>
              <Tile.Figure figureClassName="!text-2xl">{approvedTimesheetsTime?.approvedHours}</Tile.Figure>
            </Tile.Body>
          </Tile>
        </div>

        {/* <AnnouncementsCard /> */}
        {DashboardData && !isNetworkError && <DashboardData />}
      </PageBody>
    </>
  );
}

export default DashboardPage;
