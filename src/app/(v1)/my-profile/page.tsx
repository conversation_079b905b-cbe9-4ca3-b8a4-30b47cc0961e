'use client';
import React from 'react';
import { <PERSON>Eye, GoBack, InfoEdit, Cross, AddPop } from '../../svgs';

import { useSession } from "next-auth/react";
const ChangePassword = () => {
  const { data: session } = useSession();
  return (
<>
          <div className="parent flex flex-col justify-center align-middle items-center w-full break-words">
              <div className="main w-full flex justify-start">
                  <h1 className="font-poppins text-2xl text-[#003FBC] font-medium px-8 pb-8">My Profile</h1>
              </div>
              <div className="alldetails 2xl:w-[1183px] xl:w-[95%]  w-[90%]  px-0 relative ">
                  <div className="info bg-[#003FBC] sm:h-[200px] sm:py-0 py-6 h-fit flex sm:flex-row flex-col sm:justify-start justify-center sm:align-bottom align-middle sm:items-end items-center px-10 overflow-visible relative" >
                      <div className="mainprofileimage flex flex-row align-bottom items-end">
                          <div className="profileimage mb-[-30px] z-50 flex relative"> 
                              <img className="p-3 border-2 border-white rounded-full  z-40" src="https://www.gravatar.com/avatar/?d=mp&&s=130" alt="profile image" />
                              {/* /assets/images/profile/profile.png */}
                          </div>
                          <div className="profileimageedit bg-[#37B623] rounded-full ml-[-60px] z-50 mb-[-20px] cursor-pointer">
                              <img className="p-3  z-50" src="/assets/images/profile/image-edit.png" alt="profile image" />
                          </div>
                      </div>
                      <div className="mainprofilenames flex flex-col sm:pl-11 pl-0 pb-8 align-bottom sm:items-start items-center sm:mt-0 mt-9 ">
                          <h2 className="text-[26px] text-white font-medium sm:text-left text-center">Muhammad Zubair</h2>
                          <p className="text-lg text-white font-normal  sm:text-left text-center">Software Engineer</p>
                      </div>
                  </div>
                  <div className="detailedcols flex lg:flex-row flex-col w-full ">
                      <div className="profiles-box personal flex flex-col lg:w-1/2 w-full py-10 lg:px-8 px-4 mr-3 lg:mb-0 mb-5">
                          <h2 className="font-semibold text-lg text-black ">Personal Detail</h2>
                          <div className="fname flex flex-row pb-8 pt-7">
                              <h3 className="text-sm text-black font-normal w-1/2">First name</h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2">Muhammad</p>
                          </div>
                          <div className="lname flex flex-row pb-8">
                              <h3 className="text-sm text-black font-normal w-1/2">Last name</h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2">Zubair</p>
                          </div>
                          <div className="eid flex flex-row pb-8">
                              <h3 className="text-sm text-black font-normal w-1/2">Employee ID</h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2">dev-016</p>
                          </div>
                          <div className="ondate flex flex-row pb-8">
                              <h3 className="text-sm text-black font-normal w-1/2">OnBoarding Date</h3>
                              <p className="text-sm text-[#666666] font-semibold w-1/2">wednesday - 22th May 2022</p>
                          </div>
                          <div className="designation flex flex-row">
                              <h3 className="text-sm text-black font-normal w-1/2">Designation</h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2">Software Engineer</p>
                          </div>
                      </div>
                      <div className="profiles-box personal flex flex-col lg:w-1/2 w-full py-10 lg:px-8 px-4 lg:ml-3 ml-0">
                          <h2 className="font-semibold text-lg text-black ">Personal Detail</h2>
                          <div className="fname flex flex-row pb-8 pt-7">
                              <h3 className="text-sm text-black font-normal w-1/2">Joining date:<span className="text-[#FF0000]">*</span></h3>
                              <p className="text-sm text-[#666666] font-semibold w-1/2">wednesday - 22th May 2022</p>
                          </div>
                          <div className="lname flex flex-row pb-8">
                              <h3 className="text-sm text-black font-normal w-1/2">Job Role:<span className="text-[#FF0000]">*</span></h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2">employee</p>
                          </div>
                          <div className="eid flex flex-row pb-8">
                              <h3 className="text-sm text-black font-normal w-1/2">Salary:<span className="text-[#FF0000]">*</span></h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2">******</p>
                          </div>
                          <div className="ondate flex flex-row pb-8">
                              <h3 className="text-sm text-black font-normal w-1/2">Increment cycle:<span className="text-[#FF0000]">*</span></h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2">annual</p>
                          </div>
                          <div className="designation flex flex-row ">
                              <h3 className="text-sm text-black font-normal w-1/2">Jobe Type</h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2">full time</p>
                          </div>
                      </div>
                  </div>
                  <div className="detailedcols flex lg:flex-row flex-col w-full  mt-5">
                      <div className="profiles-box personal flex flex-col lg:w-1/2 w-full py-10 lg:px-8 px-4 lg:mr-3 mr-0 lg:mb-0 mb-5">
                          <h2 className="font-semibold text-lg text-black ">Contact Details</h2>
                          <div className="fname flex flex-row pb-8 pt-7">
                              <h3 className="text-sm text-black font-normal w-1/2">official email</h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2"><EMAIL></p>
                          </div>
                          <div className="lname flex flex-row pb-8">
                              <h3 className="text-sm text-black font-normal w-1/2">personal email</h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2"><EMAIL></p>
                          </div>
                          <div className="eid flex flex-row ">
                              <h3 className="text-sm text-black font-normal w-1/2">pnone number</h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2">+92 336 123 654</p>
                          </div>
                      </div>
                      <div className="profiles-box personal flex flex-col lg:w-1/2 w-full py-10 lg:px-8 px-4 lg:ml-3 ml-0">
                          <div className="flex flex-row">
                              <h2 className="font-semibold text-lg text-black w-1/2">Account Detail<span className="text-[#FF0000]">*</span></h2>
                              <div className="editeimg w-1/2 flex items-end justify-end cursor-pointer" >
                              <InfoEdit />
                              </div>
                          </div>
                          <div className="fname flex flex-row pb-8 pt-7">
                              <h3 className="text-sm text-black font-normal w-1/2">Account Title:</h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2">muhammad zubair</p>
                          </div>
                          <div className="lname flex flex-row ">
                              <h3 className="text-sm text-black font-normal w-1/2">Account no:</h3>
                              <p className="text-sm text-[#666666] font-normal w-1/2">1234-5678-9101-1121</p>
                          </div>
                      </div>
                  </div>
                  <div className="detailedcols flex flex-row w-full pb-4  mt-5">

                      <div className="profiles-box personal flex flex-col w-full py-10 lg:px-8 px-4">

                          <div className="flex flex-row">
                              <h2 className="font-semibold text-lg text-black w-2/3">Emergency Contact<span className="text-[#FF0000]">*</span></h2>
                              <div className="editeimg w-1/3 flex items-end justify-end cursor-pointer" >
                              <InfoEdit />
                              
                              


                              </div>
                          </div>
                          <div className="flex lg:flex-row flex-col w-full">
                              <div className="personal flex flex-col lg:w-1/2 w-full pt-10 ">
                                  <h2 className="font-normal text-lg text-black lg:w-1/2 w-full">Emergency Contact 1</h2>

                                  <div className="fname flex flex-row pb-8 pt-7">
                                      <h3 className="text-sm text-black font-normal w-1/2">Name:</h3>
                                      <p className="text-sm text-[#666666] font-normal w-1/2">Ahson Ali</p>
                                  </div>
                                  <div className="lname flex flex-row pb-8">
                                      <h3 className="text-sm text-black font-normal w-1/2">Phone Number</h3>
                                      <p className="text-sm text-[#666666] font-normal w-1/2">+92 336 123 654</p>
                                  </div>
                                  <div className="lname flex flex-row ">
                                      <h3 className="text-sm text-black font-normal w-1/2">Relation:</h3>
                                      <p className="text-sm text-[#666666] font-normal w-1/2">Brother</p>
                                  </div>
                              </div>
                              <div className="personal flex flex-col lg:w-1/2 w-full pt-10 ">
                                  <h2 className="font-normal text-lg text-black lg:w-1/2 w-full">Emergency Contact 2</h2>

                                  <div className="fname flex flex-row pb-8 pt-7">
                                      <h3 className="text-sm text-black font-normal w-1/2">Name:</h3>
                                      <p className="text-sm text-[#666666] font-normal w-1/2">Ahson Ali</p>
                                  </div>
                                  <div className="lname flex flex-row pb-8">
                                      <h3 className="text-sm text-black font-normal w-1/2">pnone number</h3>
                                      <p className="text-sm text-[#666666] font-normal w-1/2">+92 336 123 6541</p>
                                  </div>
                                  <div className="lname flex flex-row ">
                                      <h3 className="text-sm text-black font-normal w-1/2">Relation:</h3>
                                      <p className="text-sm text-[#666666] font-normal w-1/2">Brother</p>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>






              </div>

              <div className="poppup flex flex-col rounded-2xl px-5 py-8 bg-white xl:w-[815px] w-[90%]">
                  <div className="flex flex-row mb-8">
                      <h2 className="text-[22px] font-normal text-black w-1/2"> Edit Emergency Contact</h2>
                      <div className="w-1/2 flex justify-end cursor-pointer">
                      <Cross />
                      </div>
                  </div>
                  <div className="ppform flex flex-col justify-center">
                      <h3 className="text-lg font-normal text-black">Emergency Contact 1</h3>
                      <form className="flex lg:flex-row flex-col xl:flex-nowrap lg:flex-wrap w-full" action="submit">
                          <div className="input flex flex-col lg:w-1/3 w-full ">
                              <label className="text-base text-black font-normal" htmlFor="Name">Name:</label>
                              <input className="border border-[#78848F] rounded-xl text-black bg-[#F6F7F9] p-3" type="text" />
                          </div>
                          <div className="input flex flex-col lg:w-1/3 w-full lg:mx-4 mx-0 lg:my-0 my-4">
                              <label className="text-base text-black font-normal" htmlFor="Phone">Phone:</label>
                              <input className="border border-[#78848F] rounded-xl text-black bg-[#F6F7F9] p-3" type="tel" />
                          </div>
                          <div className="input flex flex-col lg:w-1/3 w-full">
                              <label className="text-base text-black font-normal" htmlFor="relation">Relation:</label>
                              <input className="border border-[#78848F] rounded-xl text-black bg-[#F6F7F9] p-3" type="text" />
                          </div>
                      </form>
                      <h3 className="text-lg font-normal text-black mt-5">Emergency Contact 1</h3>
                      <form className="flex lg:flex-row flex-col xl:flex-nowrap lg:flex-wrap w-full" action="submit">
                          <div className="input flex flex-col lg:w-1/3 w-full">
                              <label className="text-base text-black font-normal" htmlFor="Name">Name:</label>
                              <input className="border border-[#78848F] rounded-xl text-black bg-[#F6F7F9] p-3" type="text" />
                          </div>
                          <div className="input flex flex-col lg:w-1/3 w-full lg:mx-4 mx-0 lg:my-0 my-4">
                              <label className="text-base text-black font-normal" htmlFor="Phone">Phone:</label>
                              <input className="border border-[#78848F] rounded-xl text-black bg-[#F6F7F9] p-3" type="tel" />
                          </div>
                          <div className="input flex flex-col lg:w-1/3 w-full3">
                              <label className="text-base text-black font-normal" htmlFor="relation">Relation:</label>
                              <input className="border border-[#78848F] rounded-xl text-black bg-[#F6F7F9] p-3" type="text" />
                          </div>

                      </form>
                      <div className="btns flex flex-col items-center justify-center">
                      <button className=" w-full text-white flex items-center justify-center text-center rounded-xl bg-[#003FBC] py-4 mt-6 cursor-pointer" > <AddPop></AddPop>ADD MORE ROW</button>
                      <button className=" sm:w-[350px] w-[60%] text-white text-center rounded-xl bg-[#003FBC] py-4 mt-6" type="submit">update</button>
                      </div>
                  </div>
              </div>
          </div>
</>
  );
};
export default ChangePassword;