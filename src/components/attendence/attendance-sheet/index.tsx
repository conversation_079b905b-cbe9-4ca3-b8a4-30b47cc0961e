'use client';
import React, { useEffect, useState, useContext } from 'react';
import AdminHeader from '~/app/admin/components/AdminHeader';
import { PageBody } from '~/core/ui/Page';
import { Section, SectionBody } from '~/core/ui/Section';
import { TextFieldLabel } from '~/core/ui/TextField';
import CompanyEmployeesSelector from './CompanyEmployeesSelector';
import Button from '~/core/ui/Button';
import { endOfMonth, format, startOfMonth } from 'date-fns';
import Spinner from '~/core/ui/Spinner';
import EmployeeAttendance from './Employee';
import Tile from '~/core/ui/Tile';
import { useLazyGetAttendancesQuery } from '~/app/_utils/redux/slice/emptySplitApi';
import { convertTo12HourFormat, calculatePendingWorkingHours, calculateMissingHours, adjustToLocalDate } from '~/common/common';
import { Calendar } from '~/common/calender';
// import UserSessionContext from '~/core/session/contexts/user-session';
import { toast } from 'react-toastify';
import { attendenceSheetText, frontendRolesText, timeSheetsText } from '~/common/constant';
import { AttendanceSheetProps, AttendancePayload } from '~/types/attendence/attendenceTypes';
import { Dash } from "~/app/svgs";
import Addition from "~/app/svgs/addition";
import { useSession } from 'next-auth/react';
import { MenuItem, Select, Typography } from '@mui/material';
import EmptyListComponent from '~/core/ui/EmptyListComponent';
import dayjs from 'dayjs';
import Badge from '~/core/ui/Badge';
import UserIcon from '~/app/svgs/UserIcon';
import FilterBackground from '~/core/ui/FilterBackground';
import CustomSelect from '~/core/ui/CustomSelect';
import Typeahead from '~/core/ui/TypeAhead';


// Define types for employee data
interface Employee {
    id: string | number;
    name: string;
}

const bufferLateOptions = [
    { label: 'All', value: 'all' },
    { label: 'On Time', value: 'on time' },
    { label: 'Late', value: 'late' },
];

const AttendanceSheet: React.FC<AttendanceSheetProps> = ({ employees, defaultValue, pageHeader, subHeading }) => {
    // const ctx = useContext(UserSessionContext);
    const { data: session, status } = useSession();
    const userRoleInSession = session?.user?.role;
    const userIdInSession = session?.user?.id
    const currentDate = new Date();
    // State hooks
    // const [employeesData, setEmployeesData] = useState<Employee[]>(employees || []);
    const [employeeId, setEmployeeId] = useState<string>(defaultValue ? String(defaultValue) : "");
    const [firstTimeLoader, setFirstTimeLoader] = useState(true);
    const [showFilter, setShowFilter] = useState(false);

    const [dateRange, setDateRange] = useState<Date[]>([startOfMonth(currentDate), endOfMonth(currentDate)]);
    const [loader, setLoader] = useState(true);
    const [attendanceList, setAttendanceList] = useState<any>(null);
    const [getAttendances] = useLazyGetAttendancesQuery();
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [bufferLate, setBufferLate] = useState("")
    const [employeeNameForHeading, setEmployeeNameForHeading] = useState("")
    const [employementStatusForHeading, setEmployementStatusForHeading] = useState("")
    const [isClearFilterClicked, setIsClearFilterClicked] = useState<boolean>(false);
    const [networkError, setNetworkError] = useState(false);


    // Check if error is network related
    const isNetworkError = (error: any) => {
        return !navigator.onLine ||
               error?.name === 'TypeError' ||
               error?.message?.includes('fetch') ||
               error?.message?.includes('network') ||
               error?.message?.includes('Failed to fetch') ||
               error?.status === 'FETCH_ERROR';
    };

    // Fetch attendance list
    const getAttendanceList = async (payload: AttendancePayload) => {
        setLoader(true);
        setAttendanceList(null);
        setNetworkError(false);

        try {
            const queryParams = new URLSearchParams({
                employeeId: employeeId || userIdInSession,
                fromDate: payload?.fromDate,
                toDate: payload?.toDate,
                page: payload?.page?.toString(),
                bufferLate: payload?.bufferLate === "all" ? "" : payload?.bufferLate
            } as Record<string, string>).toString();

            const response = await getAttendances(queryParams);
            if (response?.data) {
                // console.log("response", response.data)
                setAttendanceList(response.data);
                setTotalPages(response.data.totalPages || 1);
                setNetworkError(false);
            }
            if (response?.error) {
                console.log("response", response)
                if (isNetworkError(response.error)) {
                    setNetworkError(true);
                } else if ('data' in response.error) {
                    const errorMessage = (response.error.data as { error: string }).error || 'Error loading data';
                    toast.error(errorMessage);
                }
            }
        } catch (error) {
            console.error('Attendance fetch error:', error);
            if (isNetworkError(error)) {
                setNetworkError(true);
            } else {
                toast.error('Error loading data');
            }
        } finally {
            setLoader(false);
        }
    };

    // Fetch user attendance on role or date/page changes
    useEffect(() => {
        if (!session || session === undefined) return;
        if (session) {
            const payload: AttendancePayload = {
                employeeId: userIdInSession || '',
                fromDate: adjustToLocalDate(dateRange[0]),
                toDate: dateRange[1].toISOString().split('T')[0],
                bufferLate,
                page,
            };
            getAttendanceList(payload).finally(() => setFirstTimeLoader(false));
        }
    }, [userRoleInSession, page]);
    // console.log("empId", employeeId)
    // console.log("attendanceL", attendanceList)
    useEffect(() => {
        if (defaultValue !== false) {
            const getEmployeesForName = async () => {
                try {
                    const response = await fetch(`${process.env.API_URL}/api/employee/getAllEmployees`, { cache: 'no-store' });
                    const employees = await response.json();
                    const employee = employees.employees.find((emp: any) => emp._id === defaultValue);
                    setEmployeeNameForHeading(employee?.name)
                    setEmployementStatusForHeading(employee?.employementStatus)
                    console.log("woek", employee)
                } catch (e) {
                    console.log(e)
                }
            }
            getEmployeesForName();
        }

    }, [defaultValue])

    useEffect(() => {
        if (isClearFilterClicked === false) return;
        if (isClearFilterClicked) {
            if (!session || session === undefined) return;
            if (session) {
                const payload: AttendancePayload = {
                    employeeId: userIdInSession || '',
                    fromDate: adjustToLocalDate(dateRange[0]),
                    toDate: dateRange[1].toISOString().split('T')[0],
                    bufferLate,
                    page,
                };
                getAttendanceList(payload).finally(() => setFirstTimeLoader(false));
            }
            setIsClearFilterClicked(false);
        }
    }, [isClearFilterClicked]);


    // Render content
    return (

        <div className="flex flex-1 flex-col">

            <AdminHeader
                backToAppText={undefined}
                backToAppURL={undefined}
            >
                {subHeading && <span className='text-gray-500'>{subHeading}</span>}{pageHeader ? pageHeader : attendenceSheetText.employeeAttendance}
            </AdminHeader>


            <PageBody>
                {/* <Section>
                    <SectionBody className="space-y-4 bg-white rounded">
                        <div
                            className="flex items-center space-x-3 cursor-pointer"
                            onClick={() => setShowFilter((prevState) => !prevState)}
                        >
                            {showFilter ? <Dash /> : <Addition />}
                            <span>{attendenceSheetText.filters}</span>
                        </div>

                        {showFilter && (
                            <>
                                <div className="flex gap-4 min-w-0">
                                    {(!defaultValue && (userRoleInSession === frontendRolesText.admin || userRoleInSession === frontendRolesText.hr)) && (
                                        <div className="flex flex-col w-full">

                                            <TextFieldLabel>{attendenceSheetText.employees}</TextFieldLabel>
                                            <CompanyEmployeesSelector
                                                employees={employees}
                                                value={employeeId}
                                                userIdInSession={userIdInSession}
                                                userRoleInSession={userRoleInSession}
                                                onChange={(id) => setEmployeeId(String(id))}

                                            />
                                        </div>

                                    )}

                                    <div className="flex flex-col w-full">
                                        <TextFieldLabel>{attendenceSheetText.dateRange}</TextFieldLabel>
                                        <Calendar
                                            initialValue={dateRange}
                                            onChangeDate={(dates: React.SetStateAction<Date[]>) => setDateRange(dates)}
                                            filterdate={undefined}
                                        />
                                    </div>

                                    <div className="flex flex-col w-full">
                                        <TextFieldLabel>{attendenceSheetText.bufferLate}</TextFieldLabel>
                                        <Select
                                            placeholder='All'
                                            variant="outlined"
                                            displayEmpty
                                            value={bufferLate}
                                            onChange={(e)=>setBufferLate(e.target.value)}
                                            size='small'
                                            renderValue={(value: unknown) => {
                                                if (!value) {
                                                    return <Typography className='text-gray-400'>{attendenceSheetText.All}</Typography>;
                                                }
                                                return <>{String(value)}</>;
                                            }}
                                            fullWidth
                                            MenuProps={{
                                                PaperProps: {
                                                    style: {
                                                        maxHeight: 200, // Optional: limit the height of the dropdown
                                                    },
                                                },
                                                anchorOrigin: {
                                                    vertical: 'bottom',  // Position the menu below the Select
                                                    horizontal: 'left',  // Align to the left of the Select
                                                },
                                                transformOrigin: {
                                                    vertical: 'top',  // Transform from the top to make the dropdown appear below
                                                    horizontal: 'left', // Align the top-left of the dropdown with the Select
                                                },
                                            }}
                                            sx={{minWidth:'300px'}}
                                        >
                                            <MenuItem value={"on time"}>on time</MenuItem>
                                            <MenuItem value={"late"}>late</MenuItem>
                                        </Select>
                                    </div>


                                </div>
                                <div className="flex flex-row justify-end gap-2">
                                    <Button
                                        variant="text"
                                        onClick={() => {
                                            setEmployeeId("");
                                            setDateRange([startOfMonth(currentDate), currentDate]);
                                            setPage(1)
                                            setBufferLate("")
                                        }}
                                    >
                                        {attendenceSheetText.clearFilter}
                                    </Button>
                                    <Button
                                        loading={loader}
                                        onClick={async() => {
                                            const payload: AttendancePayload = {
                                                employeeId: employeeId.toString(),
                                                fromDate: dateRange[0].toISOString().split('T')[0],
                                                toDate: dateRange[1].toISOString().split('T')[0],
                                                bufferLate: bufferLate,
                                                page: 1,
                                            };

                                            await getAttendanceList(payload);
                                            setPage(1)
                                        }}
                                    >
                                        {attendenceSheetText.search}
                                    </Button>
                                </div>
                            </>
                        )}
                    </SectionBody>
                </Section> */}

                <FilterBackground
                    onSearchClick={async () => {
                        const payload: AttendancePayload = {
                            employeeId: employeeId.toString(),
                            // fromDate: dateRange[0].toISOString().split('T')[0],
                            // toDate: dateRange[1].toISOString().split('T')[0],
                            fromDate: format(dateRange[0], timeSheetsText.dateFormat),
                            toDate: format(dateRange[1], timeSheetsText.dateFormat),
                            bufferLate: bufferLate,
                            page: 1,
                        };

                        await getAttendanceList(payload);
                        setPage(1)
                    }}
                    onClearFilterClick={() => {
                        setEmployeeId("");
                        setDateRange([startOfMonth(currentDate), endOfMonth(currentDate)]);
                        setPage(1)
                        setBufferLate("")
                        setIsClearFilterClicked(true)
                    }}
                    onSearchLoading={loader}
                >
                    <>
                        <div className="flex gap-4 min-w-0">
                            {(!defaultValue && (userRoleInSession === frontendRolesText.admin || userRoleInSession === frontendRolesText.hr)) && (
                                <div className="flex flex-col w-full">

                                    <TextFieldLabel>{attendenceSheetText.employeeName}</TextFieldLabel>
                                    <CompanyEmployeesSelector
                                        employees={employees}
                                        value={employeeId}
                                        userIdInSession={userIdInSession}
                                        userRoleInSession={userRoleInSession}
                                        onChange={(id) => setEmployeeId(String(id))}

                                    />
                                    {/* <Typeahead
                                        options={[
                                            { label: 'Apple', value: 'apple' },
                                            { label: 'Banana', value: 'banana' },
                                            { label: 'Cherry', value: 'cherry' },
                                        ]}
                                        onSelect={(selected) => console.log(selected)}
                                        placeholder="Select a fruit"
                                    /> */}
                                </div>
                            )}
                            <div className="flex flex-col w-full">
                                <TextFieldLabel>{attendenceSheetText.selectDate}</TextFieldLabel>
                                <Calendar
                                    initialValue={dateRange}
                                    onChangeDate={(dates: React.SetStateAction<Date[]>) => setDateRange(dates)}
                                    filterdate={undefined}
                                />
                            </div>
                            <div className="flex flex-col w-full">
                                <TextFieldLabel>{attendenceSheetText.status}</TextFieldLabel>
                                <CustomSelect
                                    placeholder="Select Status"
                                    value={bufferLate}
                                    options={bufferLateOptions}
                                    onChange={(val: string) => setBufferLate(val)}
                                />
                            </div>
                        </div>
                        {/* <div className="flex flex-row justify-end gap-2">
                            <Button
                                variant="text"
                                onClick={() => {
                                    setEmployeeId("");
                                    setDateRange([startOfMonth(currentDate), currentDate]);
                                    setPage(1)
                                    setBufferLate("")
                                }}
                            >
                                {attendenceSheetText.clearFilter}
                            </Button>
                            <Button
                                loading={loader}
                                onClick={async () => {
                                    const payload: AttendancePayload = {
                                        employeeId: employeeId.toString(),
                                        fromDate: dateRange[0].toISOString().split('T')[0],
                                        toDate: dateRange[1].toISOString().split('T')[0],
                                        bufferLate: bufferLate,
                                        page: 1,
                                    };

                                    await getAttendanceList(payload);
                                    setPage(1)
                                }}
                            >
                                {attendenceSheetText.search}
                            </Button>
                        </div> */}
                    </>
                </FilterBackground>
                {attendanceList && <div className="grid grid-cols-1 gap-6 mt-8 mb-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 bg-white p-6 rounded-xl shadow-sm">

                    {employeeNameForHeading && <div className='col-span-full flex items-center space-x-2'>
                        <UserIcon />
                        {employeeNameForHeading && <p className='text-xl font-bold col-span-full'>{employeeNameForHeading}</p>}
                        {employementStatusForHeading === "internship" && <Badge size={'small'} className={'inline-flex'} color={'info'}>{employementStatusForHeading[0]?.toUpperCase() + employementStatusForHeading?.slice(1)}</Badge>}
                        {employementStatusForHeading === "probation" && <Badge size={'small'} className={'inline-flex'} color={'warn'}>{employementStatusForHeading[0]?.toUpperCase() + employementStatusForHeading?.slice(1)}</Badge>}
                        {employementStatusForHeading === "permanent" && <Badge size={'small'} className={'inline-flex'} color={'success'}>{employementStatusForHeading[0]?.toUpperCase() + employementStatusForHeading?.slice(1)}</Badge>}
                    </div>
                    }

                    <Tile>
                        <Tile.Header>
                            <Tile.Heading>{attendenceSheetText.lateCounts}</Tile.Heading>
                        </Tile.Header>
                        <Tile.Body>
                            <Tile.Figure figureClassName={`${attendanceList?.lateCounts > 4 && "text-red-500"}`}>
                                {attendanceList?.lateCounts || 0}
                            </Tile.Figure>
                            {/* <Tile.Trend trend="up">Increase</Tile.Trend> */}
                        </Tile.Body>
                    </Tile>

                    <Tile>
                        <Tile.Header>
                            <Tile.Heading>{attendenceSheetText.leaves}</Tile.Heading>
                        </Tile.Header>
                        <Tile.Body>
                            <Tile.Figure >
                                {attendanceList?.leavesCount}
                            </Tile.Figure>
                            {/* <Tile.Trend trend="up">Increase</Tile.Trend> */}
                        </Tile.Body>
                    </Tile>

                    <Tile>
                        <Tile.Header>
                            <Tile.Heading>{attendenceSheetText.workingHours}</Tile.Heading>
                        </Tile.Header>
                        <Tile.Body>
                            <Tile.Figure>

                                {attendanceList?.workingHours ? attendanceList?.workingHours?.totalHours : "-"} / {attendanceList?.requiredWorkingHours}
                            </Tile.Figure>
                            {/* <Tile.Trend trend="up">Increase</Tile.Trend> */}
                        </Tile.Body>
                    </Tile>

                    <Tile>
                        <Tile.Header>
                            <Tile.Heading>{attendenceSheetText.approvedHours}</Tile.Heading>
                        </Tile.Header>
                        <Tile.Body>
                            <Tile.Figure >
                                {attendanceList?.approvedHoursSummary?.approvedHours
                                    ? attendanceList?.approvedHoursSummary?.approvedHours
                                    : '00'}
                            </Tile.Figure>
                            {/* <Tile.Trend trend="none">Yesterday</Tile.Trend> */}
                        </Tile.Body>
                    </Tile>

                </div>}

                {(loader) && <Spinner className="mx-auto h-10 w-10 mt-5" />}

                {/* {attendanceList?.attendancesList.length === 0 && <EmptyListComponent description='No attendance record'/>} */}

                {/* Show network error when there's a network issue */}
                {networkError && !loader ? (
                    <div className="flex flex-1 items-center min-h-[50vh] justify-center mt-2">
                        <div className="text-center">
                            <h2 className="text-xl font-semibold text-gray-600">
                                {attendenceSheetText.internetIssue}
                            </h2>
                            <p className="text-gray-500 mt-2">
                                {attendenceSheetText.internetIssueDescription}
                            </p>
                        </div>
                    </div>
                ) : attendanceList?.attendancesList && attendanceList?.attendancesList.length > 0 ? (
                    <EmployeeAttendance
                        data={attendanceList.attendancesList}
                        employeeName={attendanceList.username}
                        employeeRole={attendanceList?.userRole}
                        employeeIdInResponse={attendanceList?.userID}
                        userRole={userRoleInSession}
                        page={page}
                        setPage={setPage}
                        totalPages={totalPages}
                        employeeId={employeeId}
                        defaultValue={defaultValue}
                        userRoleInSession={userRoleInSession}
                        reloadAttendanceList={() => {
                            const payload: AttendancePayload = {
                                employeeId: employeeId || userIdInSession,
                                fromDate: dateRange[0].toISOString().split('T')[0],
                                toDate: dateRange[1].toISOString().split('T')[0],
                                bufferLate,
                                page,
                            };
                            getAttendanceList(payload)
                        }}
                    />
                ) : (
                    !loader && <EmptyListComponent description='No attendance record' className='mt-2' />
                )}
            </PageBody>
        </div>
    );
};

export default AttendanceSheet;