import { useEffect, useState } from "react";
import { Dialog<PERSON>ontent, FormControl, MenuItem, Typography } from "@material-ui/core"
import { EmployementStatusChangeModalText, employementStatusesText } from "~/common/constant"
import ModalBackground from "~/core/ui/ModalBackground"
import { IEmployementStatusChangeModal } from "~/types/common/commonTypes"
import CoreButton from '~/core/ui/Button';
import { useChangeEmployementStatusMutation } from "~/app/_utils/redux/slice/emptySplitApi";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { Select } from "@mui/material";
import { routes } from "~/common/routes";
import employementStatusOptions from "~/lib/organizations/employementStatus";
import CustomSelect from "~/core/ui/CustomSelect";

const EmployementStatusChangeModal: React.FC<IEmployementStatusChangeModal> = ({ isOpen, employeeData, fetchEmployees, closeModal }) => {
  const router = useRouter();
  const [employementStatus, setEmployementStatus] = useState<string | null>()
  const [changeEmployementStatusAPI, changeEmployementStatusAPIResponse] = useChangeEmployementStatusMutation();

  useEffect(() => {
    setEmployementStatus(employeeData?.employementStatus)
  }, [employeeData])

  const handleChangeEmployementStatus = async () => {
    try {
      const response = await changeEmployementStatusAPI({
        id: employeeData?._id,
        employementStatus
      })
      if (response) {
        toast.success(response?.data?.message);
        fetchEmployees();
        router.refresh();
        closeModal();
      }else{
        // console.log("responseresponse=====>",response)
      }
      // console.log("responseresponse=====>12",response)

    } catch (e) {
      console.log(e)
    }

  }
  return (
    <ModalBackground isOpen={isOpen} closeModal={closeModal} title="Change Employement Status" maxWidth="xs" >
      <DialogContent className="px-6 mb-2">
        <Typography variant="body2" className="pb-2 font-medium">{EmployementStatusChangeModalText.employementStatus}</Typography>
        {/* <Select
          size="small"
          value={employementStatus}
          variant="outlined"
          placeholder='select'
          displayEmpty
          fullWidth
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: 400, // Optional: limit the height of the dropdown
              },
            },
            anchorOrigin: {
              vertical: 'bottom',  // Position the menu below the Select
              horizontal: 'left',  // Align to the left of the Select
            },
            transformOrigin: {
              vertical: 'top',  // Transform from the top to make the dropdown appear below
              horizontal: 'left', // Align the top-left of the dropdown with the Select
            },
          }}
          onChange={(e) => {
            setEmployementStatus(e.target.value as string | null)
          }}
        >
          <MenuItem value={employementStatusesText.internship}>{employementStatusesText.internship}</MenuItem>
          <MenuItem value={employementStatusesText.probation}>{employementStatusesText.probation}</MenuItem>
          <MenuItem value={employementStatusesText.permanent}>{employementStatusesText.permanent}</MenuItem>
        </Select> */}
        <CustomSelect
             placeholder="Select Status"
             value={employementStatus}
             options={employementStatusOptions}
             onChange={(val: string) => setEmployementStatus(val)}
         />
        {/* <div className="mt-8 text-right">
          <CoreButton
            className="px-6"
            onClick={() => handleChangeEmployementStatus()}
            disabled={employementStatus === employeeData?.employementStatus || changeEmployementStatusAPIResponse?.isLoading}
          >
            {EmployementStatusChangeModalText.submit}
          </CoreButton>
        </div> */}
        <div className="flex items-center justify-end  space-x-6 mt-4">
          <p className="cursor-pointer font-medium" onClick={closeModal}>No</p>
          <CoreButton
            className="px-6"
            onClick={() => handleChangeEmployementStatus()}
            disabled={employementStatus === employeeData?.employementStatus || changeEmployementStatusAPIResponse?.isLoading}
          >
            {EmployementStatusChangeModalText.submit}
          </CoreButton>
        </div>
      </DialogContent>
    </ModalBackground>
  )
}

export default EmployementStatusChangeModal