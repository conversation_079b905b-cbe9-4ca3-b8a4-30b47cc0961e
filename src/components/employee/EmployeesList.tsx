/**New logic */
'use client'
import React, { useEffect, useState } from 'react';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Badge from '~/core/ui/Badge';
import SearchBar from 'material-ui-search-bar';
import Link from 'next/link';
import IconButton from '~/core/ui/IconButton';
import { EllipsisHorizontalIcon } from '@heroicons/react/24/outline';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '~/core/ui/Dropdown';
import { formatDate } from '~/common/common';
import CustomPagination from '~/core/ui/CustomPagination';
import AccountStatusChangeConfirmation from './AccountStatusChangeConfirmation';
import { employeeAttendanceText } from '~/common/constant';
import EmployementStatusChangeModal from './EmployementStatusChangeModal';
import dayjs from 'dayjs';
import Spinner from '~/core/ui/Spinner';
import EmptyListComponent from '~/core/ui/EmptyListComponent';
import { useSession } from 'next-auth/react';
import { EllipsisVerticalIcon } from 'lucide-react';

interface Column {
  id: string;
  label: string;
  align?: 'left';
}

const initialColumns: readonly Column[] = [
  { id: 'serial', label: 'Sr #' },
  { id: 'empID', label: 'Employee Id' },
  { id: 'name', label: 'Name And Email' },
  { id: 'role', label: 'Role' },
  { id: 'employementStatus', label: 'Employement Status' },
  { id: 'lastSignIn', label: 'Last Sign In' },
  { id: 'joiningDate', label: 'Joining Date' },
  { id: 'status', label: 'Status' },
  { id: 'actions', label: 'Actions' },
];

export default function EmployeeList({ data, page, setPage, totalPages, keyword, setKeyword, isGetAllEmployeesLoading, fetchEmployees }: { data: any[], page: number, setPage: (number: number) => void, totalPages: number, keyword: string, setKeyword: (string: string) => void, isGetAllEmployeesLoading: any, fetchEmployees: any }) {
  const {data:session} = useSession();
  const userRoleInSession = session?.user?.role; 
  const [showAccountStatusChangeDialogue, setAccountStatusChangeDialogue] = useState(false)
  const [showEmployementStatusChangeModal, setEmployementStatusChangeModal] = useState(false)
  const [employeeID, setEmployeeID] = useState(null)
  const [isActive, setIsActive] = useState(null)
  const [employeeData, setEmployeeData] = useState({})

  const handleSearch = (searchedValue: string) => {
    setKeyword(searchedValue);
    setPage(1);
  };

  const cancelSearch = () => {
    setKeyword('');
    setPage(1);
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  return (
    <>
      {/* <div className='mt-4'>
        <SearchBar
          value={keyword}
          onChange={handleSearch}
          onCancelSearch={cancelSearch}
          placeholder="Search employees"
        />
      </div> */}
      <div style={{ marginTop: '16px', borderRadius: "0.75rem" }}></div>
      {isGetAllEmployeesLoading ? (
        // <div>Loading...</div> 

        <div className='flex items-center justify-center !w-full !h-full'>
          <Spinner className={'mx-auto !h-10 !w-10 fill-white dark:fill-white'} />
        </div>

      ) : data && data.length> 0 ?(
        <>
          <Paper sx={{ width: '100%', overflow: 'hidden', borderRadius: "0.75rem"}}>
            <TableContainer sx={{ maxHeight: 800 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    {initialColumns.map((column) => (
                      <TableCell key={column.id} align={column?.label === "Name And Email" ? "left" : "center"} sx={{whiteSpace: column?.label === "Joining Date" || column?.label === "Sr #" ? "nowrap" : ""}}>
                        <b>{column.label}</b>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data
                   .filter((employee) => {
                    if (userRoleInSession === 'hr') {
                      return employee.role !== 'hr' && employee.role !== 'admin'; // Exclude HR and Admin
                    }
                    return true; // Show all employees for other roles
                  })
                  .map((row, index) => {
                    const formattedDate = dayjs(row.lastLoginIn).format('dddd, MMM D, YYYY h:mm A');
                    return (
                      <TableRow key={row._id} >
                        <TableCell align="center">{index + 1}</TableCell>
                        <TableCell align="center">{row.empID}</TableCell>
                        <TableCell align="left">
                          <b>{row.name}</b>
                          <br />
                          {row.email}
                        </TableCell>
                        <TableCell align="center">
                          <span className='text-primary font-bold'>{row?.role?.charAt(0)?.toUpperCase() + row?.role?.slice(1) || ""}</span>
                        </TableCell>
                        <TableCell align="center">
                        <span className='text-primary font-bold'>{row?.employementStatus?.charAt(0)?.toUpperCase() + row?.employementStatus?.slice(1) || ""}</span>
                        </TableCell>
                        <TableCell align="center">{row.lastLoginIn === "Yet To Login" ? row.lastLoginIn : formattedDate || 'N/A'}</TableCell>
                        <TableCell align="center">
                          {formatDate(row.employmentHistory?.[0]?.startDate || row.createdAt)}
                        </TableCell>
                        <TableCell align="center">
                          <Badge
                            size="small"
                            className="inline-flex"
                            color={
                              row?.isActive === "onboarding" ? "info" : row?.isActive === "active" ? "success" : row?.isActive === "offboarding" ? "warn" : "error"
                            }
                          >
                            {row?.isActive === "onboarding" && "Onboarding"}
                            {row?.isActive === "active" && "Active"}
                            {row?.isActive === "offboarding" && "Offboarding"}
                            {row?.isActive === "inactive" && "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell align="center">
                        <div className='flex items-center justify-center '>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <IconButton className="border border-gray-500 rounded-full p-2">
                                <span className="sr-only">{employeeAttendanceText.openMenu}</span>
                                <EllipsisVerticalIcon className="h-4 w-4 border-1" />
                              </IconButton>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Link href={`/employees/${row._id}/update`}>{employeeAttendanceText.editEmployee}</Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Link href={`/employees/${row._id}/attendance`}>{employeeAttendanceText.viewAttendance}</Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Link href={`/employees/${row._id}/leave-quota?employeeName=${row?.name}&employementStatus=${row?.employementStatus}biz`}>
                                  {employeeAttendanceText.viewLeaveQuota}
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  setEmployementStatusChangeModal(true)
                                  setEmployeeID(row?._id)
                                  setEmployeeData(row)
                                }}
                              >
                                {employeeAttendanceText.changeEmployementStatus}
                              </DropdownMenuItem>
                              {row.isActive === "onboarding" && <DropdownMenuItem>
                                <Link href={`/onboardinglist?userID=${row?._id}&&employeeName=${row?.name}`}>
                                  {employeeAttendanceText.onboardingList}
                                </Link>
                              </DropdownMenuItem>}
                              {row.isActive === "offboarding" && <DropdownMenuItem>
                                <Link href={`/offboardinglist?userID=${row._id}&&employeeName=${row?.name}`}>
                                  {employeeAttendanceText.offboardingList}
                                </Link>
                              </DropdownMenuItem>}
                              {row?.isActive !== "offboarding" &&row?.isActive !== "onboarding"&& <DropdownMenuItem onClick={() => {
                                setEmployeeID(row._id)
                                setIsActive(row?.isActive)
                                setAccountStatusChangeDialogue(true)
                                setEmployeeData(row)

                              }}>
                                {row?.isActive === "active" && 'initiate Deactivation'}
                                {row?.isActive === "inactive" && 'Activate Account'}
                              </DropdownMenuItem>}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </TableContainer>
            {totalPages > 1 && (
            <div className=" flex items-center justify-center p-2 mt-2 bg-white shadow-md ">
              <CustomPagination
                page={page}
                count={totalPages}
                onChange={handlePageChange}
              />
            </div>
          )}
          </Paper>
          
        </>
      ):(
        <EmptyListComponent 
            description={"No user found"}
            className='mt-5 p-20'
        />
      )
      }
      {showAccountStatusChangeDialogue && <AccountStatusChangeConfirmation
        isOpen={showAccountStatusChangeDialogue}
        employeeID={employeeID}
        employeeData={employeeData}
        isActive={isActive}
        // setFilteredRows={setFilteredRows}
        fetchEmployees={fetchEmployees}
        closeModal={() => {
          setAccountStatusChangeDialogue(false)
          setEmployeeID(null)
          setIsActive(null)
          setEmployeeData({})
        }}
      />}
      {
        showEmployementStatusChangeModal && <EmployementStatusChangeModal
          isOpen={showEmployementStatusChangeModal}
          employeeData={employeeData}
          fetchEmployees={fetchEmployees}
          closeModal={() => {
            setEmployementStatusChangeModal(false)
            setEmployeeData({})
          }}
        />
      }
    </>
  );
}
