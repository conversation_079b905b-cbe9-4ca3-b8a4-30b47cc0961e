import { ContactPhone } from "@material-ui/icons"
import exp from "constants"

export const ManualTaskDetailsDialogueText={
    taskDetails:"Task Details" ,
    title:"Title",
    description:"  Description"
}
export const MonthlyCalendarText={
    noItemsAvailable:"No items available",
    more:"more"
}
export const attendenceSheetText={
    clearFilter:"Clear Filter",
    filters:"Filters",
    selectDate:"Select Date",
    status: "Status",
    attendances:"Attendances",
    employeeAttendance:"Attendance",
    employeeName:"Employees Name",
    search:"Search",
    lateCounts:"Late Counts",
    workingHours:"Working Hours",
    pendingHrs:"Pending Hrs",
    currentShiftTime:"Current Shift Time",
    attendance:'Attendance',
    leaves:"leaves",
    approvedHours: "Approved Hours",
    All:"All",
    internetIssue:"Internet issue",
    internetIssueDescription:"Please check your internet connection and try again."
}
export const addEditTimeSheetTextEnums={
    manualId:"manualId"
}
export const accountStatusChangeConfirmationText={
    Confirmation:"Confirmation",
    no:"No",
    yes:"Yes"
}
export const changeEmployeeAccountStatusApiText= {
   responseMessage:  "Account status updated successfully"
}
export const changeEmployementStatusApiText= {
  responseMessage:  "Employement status updated successfully"
}
export const updateEmployeeBoardingListApi = {
    responseMessage:  "List updated Successfully"
 }

export const onBordingText={
onboardingList:"  Onboarding List",
submit:"Submit",
submitting:"Submitting..."
}

export const offBoardingText={
    offboardingList:"Offboarding List",
    submit:"Submit",
    submitting:"Submitting..."
}
export const leavesText={
    leaves:"Leaves",
    leavesType:"Leave Type ",
    applyForLeave:"Apply for Leave",
    filters:"Filters",
    dateRange:"Date Range",
    all:"All",
    annualLeave:"Annual Leave",
    casualLeave:"Casual Leave",
    sickLeave:"Sick Leave",
    status:"Status",
    pending:"Pending",
    approved:"Approved",
    rejected:"Rejected",
    clearFilter:"Clear Filter",
    search:"Search",
    emptyList:"Empty list",
    noLeavesAvailableInTheGivenDatePleaseAddLeaves:"No leaves available in the given date",
    applyForLeaves :"Apply For Leaves"
}
export const leavesApprovalsText={
    filters:"Filters",
    dateRange:"Date Range",
    leavesType:"Leaves Type",
    all:"All",
    annualLeave:"Annual Leave",
    casualLeave:"Casual Leave",
    sickLeave:"Sick Leave",
    status:"Status",
    pending:"Pending",
    approved:"Approved",
    rejected:"Rejected",
    clearFilter:"Clear Filter",
    search:"Search",
    emptyList:"Empty list",
    noLeavesAvailableInTheGivenDatePleaseAddLeaves:"No leaves available in the given date"
}
export const employeeAttendanceText={
    openMenu:"Open menu",
    actions:"Actions",
    editEmployee:"Edit Employee",
    viewAttendance:"View Attendance",
    viewLeaveQuota:"View Leave Quota",
    changeEmployementStatus:"Change Employement Status",
    onboardingList:"Onboarding List",
    offboardingList:"Offboarding List"
}
export const reviewTimeSheetText={
    teamTimeSheets:"Team Time sheets",
    filters:"Filters",
    dateRange:"Date Range",
    projects:"Projects",
    allProjects:"All Projects",
    status:"Status",
    all:"All",
    pending:"Pending",
    approved:"Approved",
    partiallyApproved:"Partially Approved",
    clearFilter:"Clear Filter",
    search:"Search",
    rejected:"Rejected",
    emptyList:"Empty list",
    noTimeSheetAvailableTheGivenDateRange:"No time sheet available in the given date range."
}
export const dashBoardText={
    unauthorized:"Unauthorized",
    fillYourTimesheet:"Fill your timesheet",
    pleaseReview:"Please review",
    currentShiftTime:"Current Shift Time",
    checkIn :"Check In",
    checkOut :"Check Out",
    concurrentProjects:"Concurrent Projects",
    lateCounts:"Late Counts",
    leaves:"Leaves",
    workingHours:"Working Hours",
    hrs:"Hrs",
    approvedHours:"Approved Hours",
    currentMonth:"Current Month",
    yesterday:"yesterday",
    internetIssue:"Internet issue",
    internetIssueDescription:"Please check your internet connection and try again.",
    noDataAvailable:"No data available",
    noDataDescription:"Unable to load dashboard data. Please try refreshing the page.",
}
export const employeeFormText={
    createEmployee:"Create Employee",
    employeeID:"Employee ID",
    firstName:"First Name",
    lastName:"Last Name",
    fathersName:"Father`s Name",
    personalEmail:"Personal Email",
    officialEmail:"Official Email",
    phonePrefix:"+92",
    devexcelitCom:"@devexcelit.com",
    phone:"Phone",
    employementStatus: "Employement Status",
    selectEmployementStatus:"Select Employement Status",
    sendEmail:"Send Email",
    password: "password",
    confirmPassword:"Confirm Password",
    sumitting:"Submitting...",
}
export const calendarText={
    calendar:"Calendar",
    create:"Create"
}
export const createHolidayText={
    create:"Create",
    holidays:"Holidays",
    workFromHome:"WFH",
    weekendsOn:"Weekends On"
}
export const updateCalendarEntryText={
    update:"Update",
    holidays:"Holidays",
    workFromHome:"WFH",
    weekendsOn:"Weekends On"
}
export const editAttendanceText={
    editAttendance:"Edit Attendance",
    employeeName:"Employee Name",
    checkIn:"Check In",
    checkOut:"Check Out"
}
export const timeSheetStatusesText={
    pending:"Pending",
    approved:"Approved",
    rejected:"Rejected",
    partiallyApproved:"Partially Approved"
}
export const frontendRolesText={
    admin:"admin",
    hr:"hr",
    manager:"Manager",
    accountant: "accountant"
}
export const backendRolesText={
    admin:"admin",
    hr:"hr",
    manager:"Manager"
}
export const backendMethodsText={
    post:"POST",
    get:"GET",
    put:"PUT"
}
export const leaveCreationText={
    reactJs:"React js",
    reactNative:"React Native",
    nextJs:"Next js",
    flutter:"Flutter",
    azure:"Azure",
    netFramework:".NET Framework",
    nodeJs:"Node js",
    wordpress:"Wordpress",
    elementor:"Elementor",
    // others:"Others",
    projectTeamRoles:{
        manager:"Manager",
        hr:"Hr",
        bd:"Business Developer",
        scrumMaster:"Scrum Master",
        businessAnalyst:"Business Analyst",
        softwareArchitect:"Software Architect",
        leadDeveloper:"Lead Developer",
        frontendDeveloper:"Frontend Developer",
        backendDeveloper:"Backend Developer",
        fullStackDeveloper:"Full Stack Developer",
        qaEngineerTester:"QA Engineer / Tester",
        devOpsEngineer:"DevOps Engineer",
        uIUxDesigner:"UI/UX Designer",
        databaseAdministratorDba:"Database Administrator (DBA)",
        securitySpecialist:"Security Specialist",
        systemAnalyst :"System Analyst",
        technicalWriter:"Technical Writer",
        customerSupportSpecialist:"Customer Support Specialist",
        dataScientistAnalyst:"Data Scientist / Analyst",
        marketingSpecialist :"Marketing Specialist",
        others:"Others"
    },
    somethingWentWrong:"Something went wrong.",
    projectCreateProject:"Create Project",
    projectName:"Project Name",
    clientName:"Client Name",
    projectTechStack:"Project Tech Stack",
    team:"team",
    addTeamMember:"Add Team Member",
    selectProjectManagementTool:"Select Project Management Tool",
    selectProjectManagementTools:{
        devops:"Devops",
        asana:"Asana",
        trello:"Trello",
        gitHub:"Git Hub",
        others:"Others"
    },
    devopsToken:"Devops Token",
    devopsProjectName:"Devops Project Name",
    devopsOrganization:"Devops Organization",
    submit:"Submit",
    backToProjects:"Back to Projects",
    devops:"devops",
    fetchFailed:"Fetch failed:"
}
export const updateProjectText={
  reactJs:"React js",
    reactNative:"React Native",
    nextJs:"Next js",
    flutter:"Flutter",
    azure:"Azure",
    netFramework:".NET Framework",
    nodeJs:"Node js",
    wordpress:"Wordpress",
    elementor:"Elementor",
  // others:"Others",
  projectTeamRoles:{
      manager:"Manager",
      hr:"Hr",
      bd:"Business Developer",
      scrumMaster:"Scrum Master",
      businessAnalyst:"Business Analyst",
      softwareArchitect:"Software Architect",
      leadDeveloper:"Lead Developer",
      frontendDeveloper:"Frontend Developer",
      backendDeveloper:"Backend Developer",
      fullStackDeveloper:"Full Stack Developer",
      qaEngineerTester:"QA Engineer / Tester",
      devOpsEngineer:"DevOps Engineer",
      uIUxDesigner:"UI/UX Designer",
      databaseAdministratorDba:"Database Administrator (DBA)",
      securitySpecialist:"Security Specialist",
      systemAnalyst :"System Analyst",
      technicalWriter:"Technical Writer",
      customerSupportSpecialist:"Customer Support Specialist",
      dataScientistAnalyst:"Data Scientist / Analyst",
      marketingSpecialist :"Marketing Specialist",
      others:"Others"
  },
  somethingWentWrong:"Something went wrong.",
  projectEditProject:"Project / Edit Project",
  projectName:"Project Name",
  clientName:"Client Name",
  projectTechStack:"Project Tech Stack",
  team:"team",
  addTeamMember:"Add Team Member",
  selectProjectManagementTool:"Select Project Management Tool",
  selectProjectManagementTools:{
      devops:"Devops",
      asana:"Asana",
      trello:"Trello",
      gitHub:"Git Hub",
      others:"Others"
  },
  devopsToken:"Devops Token",
  devopsProjectName:"Devops Project Name",
  devopsOrganization:"Devops Organization",
  submit:"Submit",
  backToProjects:"Back to Projects",
  devops:"devops",
  fetchFailed:"Fetch failed:"
}

export const MessageText={
    errorMessage:"Something went wrong. Please try again later",
    onBoardingMessage:"Complete onboarding process first",
    offBoardingMessage:"Complete offboarding process first"
}



export const commonText={
    dashboardTabLabel: "Dashboard",
    Summary: "Summary",
    organizationSettingsTabLabel: "Organization",
    settingsTabLabel: "Settings",
    profileSettingsTabLabel: "Profile",
    subscriptionSettingsTabLabel: "Subscription",
    dashboardTabDescription: "An overview of your organization's activity and performance across all your projects.",
    settingsTabDescription: "Manage your settings and preferences.",
    emailAddress: "Email",
    password: "Password",
    repeatPassword: "Repeat Password",
    modalConfirmationQuestion: "Are you sure you want to continue?",
    imageInputLabel: "Click here to upload an image",
    cancel: "Cancel",
    backToHomePage: "Back to Home Page",
    genericServerError: "Sorry, something went wrong.",
    genericServerErrorHeading: "Sorry, something went wrong while processing your request. Please contact us if the issue persists.",
    pageNotFound: "Ops. Page not Found.",
    permissionNotFound: "Insufficient permissions",
    pageNotFoundSubHeading: "Apologies, the page you were looking for was not found",
    permissionNotFoundSubHeading: "Apologies, the page you were looking for you do not have permission",
    genericError: "Sorry, something went wrong.",
    genericErrorSubHeading: "Apologies, an error occurred while processing your request. Please contact us if the issue persists.",
    anonymousUser: "Anonymous",
    tryAgain: "Try Again",
    theme: "Theme",
    lightTheme: "Light",
    darkTheme: "Dark",
    systemTheme: "System",
    expandSidebar: "Expand Sidebar",
    collapseSidebar: "Collapse Sidebar",
    documentation: "Documentation",
    getStarted: "Get Started",
    retry: "Retry",
    contactUs: "Contact Us",
    loading: "Loading. Please wait...",
    yourOrganizations: "Your Organizations",
    continue: "Continue",
    skip: "Skip",
    changePassword: "Change Password",
    roles: {
      owner: {
        label: "Owner",
        description: "Can change any setting, invite new members and manage billing"
      },
      admin: {
        label: "Admin",
        description: "Can change some settings, invite members, perform disruptive actions"
      },
      member: {
        label: "Member",
        description: "Cannot invite members or change settings"
      }
    },
    dateFormat:"YYYY-MM-DD"
  }

  export const authText = {
    signUpHeading: "Create an account",
    signUp: "Sign Up",
    signInHeading: "Sign In To Your Account",
    signIn: "Sign In",
    signingIn: "Signing in...",
    getStarted: "Get started",
    signOut: "Sign out",
    signingUp: "Signing up...",
    orContinueWithEmail: "or continue with email",
    doNotHaveAccountYet: "Do not have an account yet?",
    alreadyHaveAnAccount: "Already have an account?",
    joinOrganizationHeading: "Join {{organization}}",
    joinOrganizationSubHeading: "You were invited to join <b>{{organization}}</Bold>",
    signUpToAcceptInvite: "Please sign in/up to accept the invite",
    clickToAcceptAs: "Click the button below to accept the invite with as <b>{{email}}</b>",
    acceptInvite: "Accept invite",
    acceptingInvite: "Accepting Invite...",
    acceptInviteSuccess: "Invite successfully accepted",
    acceptInviteError: "Error encountered while accepting invite",
    acceptInviteWithDifferentAccount: "Want to accept the invite with a different account?",
    alreadyHaveAccountStatement: "I already have an account, I want to sign in instead",
    doNotHaveAccountStatement: "I do not have an account, I want to sign up instead",
    addingToOrganization: "We are adding you to <b>{{name}}</b>. Please Wait...",
    signInWithProvider: "Sign in with {{provider}}",
    signInWithPhoneNumber: "Sign in with Phone Number",
    passwordHint: "Ensure it's at least 6 characters",
    repeatPasswordHint: "Type your password again",
    repeatPassword: "Repeat password",
    passwordsDoNotMatch: "The passwords do not match",
    passwordForgottenQuestion: "Password forgotten?",
    passwordResetLabel: "Reset Password",
    passwordResetSubheading: "Enter your email address below. You will receive a link to reset your password.",
    passwordResetSuccessMessage: "Check your Inbox! We emailed you a link for resetting your Password.",
    passwordRecoveredQuestion: "Password recovered?",
    passwordLengthError: "Please provide a password with at least 6 characters",
    sendEmailLink: "Send Email Link",
    sendingEmailLink: "Sending Email Link...",
    sendLinkSuccess: "We sent you a link to your email! Follow the link to sign in.",
    sendLinkSuccessToast: "Link successfully sent",
    getNewLink: "Get a new link",
    verificationCode: "Verification Code",
    verificationCodeHint: "Enter the code we sent you by SMS",
    verificationCodeSubmitButtonLabel: "Submit Verification Code",
    sendingMfaCode: "Sending Verification Code...",
    verifyingMfaCode: "Verifying code...",
    sendMfaCodeError: "Sorry, we couldn't send you a verification code",
    verifyMfaCodeSuccess: "Code verified! Signing you in...",
    verifyMfaCodeError: "Ops! It looks like the code is not correct",
    reauthenticate: "Reauthenticate",
    reauthenticateDescription: "For security reasons, we need you to re-authenticate",
    errorAlertHeading: "Sorry, we could not authenticate you",
    emailConfirmationAlertHeading: "We sent you a confirmation email.",
    emailConfirmationAlertBody: "Welcome! Please check your email and click the link to verify your account.",
    resendLink: "Resend link",
    resendLinkSuccess: "We sent you a new link to your email! Follow the link to sign in.",
    authenticationErrorAlertHeading: "Authentication Error",
    authenticationErrorAlertBody: "Sorry, we could not authenticate you. Please try again.",
    sendEmailCode: "Get code to your Email",
    sendingEmailCode: "Sending code...",
    enterYourNewPassword: "Enter your new password",
    differentPasswordFromOld: "Your new password must be different from previous passwords.",
    updateYourPassword: "Update your password",
    signInPage:{
      metadeta:{
        title:"Sign In"
      }
    },
    errors: {
      InvalidLoginCredentials: "The credentials entered are invalid",
      UserAlreadyRegistered: "This credential is already in use. Please try with another one.",
      EmailNotConfirmed: "Please confirm your email address before signing in",
      default: "We have encountered an error. Please ensure you have a working internet connection and try again",
      generic: "Sorry, we weren't able to authenticate you. Please try again.",
      link: "Sorry, we encountered an error while sending your link. Please try again.",
      codeVerifierMismatch: "It looks like you're trying to sign in using a different browser than the one you used to request the sign in link. Please try again using the same browser.",
      inValidCredentials: "The credentials entered are invalid"
    }
  }

  export const passwordResetPageText={
    heading:"auth:"
  }

  export const mobileAppNavigationText ={
    organizationHeading : "Select an organization below to switch to it"
  }

  export const step1DetailsText ={
      personalDetails:"Personal Details",
      pleaseManageEmployeePersonalDetails:"Please manage employee personal details",
      next:"Next",
  }

  export const step2OfficialDetailsText={
    officialDetails:"Official Details",
    addEmployeeOfficialDetails:"Add Employee Official Detail",
    back:"Back",
  }

  export const step3BankingDetailText={
    bankingDetails:"Banking Details",
    addEmployeebankingDetails:"Add Employee Banking Detail",
    back:"Back",

  }

  export const step4EmergencyContact={
      emergencyContacts:"Emergency Contact",
      addEmergencyContact:"Add Employee Emergency Contact",
      back:"Back",
  }

  export const step5WorkHistoryText={
    employmentHistory:"Employment History",
    addEmployementHistory:"Add Employment History",
    back:"Back",
  }

  export const step6SummaryText={
  updateUser:"Update",
  submitUser:"Submit",
  personalDetails:"Personal Details",
  personalDetailsAre:"Personal Details are",
  officialDetails:"Official Details",
  officialDetailsAre:"Official Details are",
  bankingDetails:"Banking Details",
  bankingDetailsAre:"Banking Details are",
  bankName:"Bank Name",
  accountNumber:"Account Number",
  emergencyDetails:"Emergency Details",
  emergencyDetailsAre:"Emergency Details are",
  contactName:"Contact Name",
  phoneNumber:"Phone Number",
  contactRelation:"Contact Relation",
  employmentDetails:"Employment Details",
  employmentDetailsAre:"Employment Details are",
  designation:"Designation",
  date:"Date",
  salary:"Salary",
  back:"Back",
  companyEmail:"Company Email",
  jobType:"Job Type",
  firstName:"First Name",
  lastName:"Last Name",
  fatherName:"Father's Name",
  email:"Email",

  }

  export const organizationInfoSetupText={
    setupOrganization: "Setup Organization",
    setupOrganizationDescription: "Welcome! First, let's setup your organization.",
    organizationNameLabel: "Organization name",
    errorAlertHeading: "There was an error completing your onboarding.",
    inviteMembers: "Invite members",
    inviteMembersDescription: "Invite your team members to join your organization.",
    info: "Details",
    invites: "Invites",
    complete: "Complete",
    successStepHeading: "You're all set! You can now start using the app.",
    continue: "Continue to your Dashboard",
    settingAccount: "We're setting up your account. Please wait..."
  }

  export const officialDetailsContactFormText={
    employeeID:"Employee ID",
    companyEmailAddress:"Company Email Address",
    jobType:"Job Type",
    shiftStartTime:"Shift Start Time",
    shiftEndTime:"Shift End Time",
    ZKTeckoUsers:"ZKtecko Users",
  }

  export const organization ={
    generalTabLabel: "General",
    generalTabLabelSubheading: "Manage your Organization",
    membersTabLabel: "Members",
    emailSettingsTab: "Email",
    membersTabSubheading: "Manage and Invite members",
    inviteMembersPageSubheading: "Invite members to your organization",
    createOrganizationModalHeading: "Create Organization",
    organizationNameLabel: "Organization Name",
    createOrganizationSubmitLabel: "Create Organization",
    createOrganizationSuccess: "Organization created successfully",
    createOrganizationError: "Organization not created. Please try again.",
    createOrganizationLoading: "Creating organization...",
    settingsPageLabel: "General",
    createOrganizationDropdownLabel: "New organization",
    changeRole: "Change Role",
    removeMember: "Remove",
    inviteMembersSuccess: "Members invited successfully!",
    inviteMembersError: "Sorry, we encountered an error! Please try again",
    inviteMembersLoading: "Inviting members...",
    removeInviteButtonLabel: "Remove invite",
    addAnotherMemberButtonLabel: "Add Another One",
    inviteMembersSubmitLabel: "Send Invites",
    removeMemberModalHeading: "You are removing this user",
    removeMemberSuccessMessage: "Member removed successfully",
    removeMemberErrorMessage: "Sorry, we encountered an error. Please try again",
    removeMemberLoadingMessage: "Removing member...",
    removeMemberSubmitLabel: "Remove User from Organization",
    chooseDifferentRoleError: "Role is the same as the current one",
    updateRoleLoadingMessage: "Updating role...",
    updateRoleSuccessMessage: "Role updated successfully",
    updatingRoleErrorMessage: "Sorry, we encountered an error. Please try again.",
    updateMemberRoleModalHeading: "Update Member's Role",
    memberRoleInputLabel: "Member role",
    updateRoleSubmitLabel: "Update Role",
    transferOwnership: "Transfer Ownership",
    deleteInviteModalHeading: "Deleting Invite",
    deleteInviteSuccessMessage: "Invite deleted successfully",
    deleteInviteErrorMessage: "Invite not deleted. Please try again.",
    deleteInviteLoadingMessage: "Deleting invite. Please wait...",
    confirmDeletingMemberInvite: "You are deleting the invite to <b>{{ email }}</b>",
    transferOwnershipDisclaimer: "You are transferring ownership of the selected organization to <b>{{ member }}</b>. Your new role will be <b>$t(common:roles.admin.label)</b>.",
    transferringOwnership: "Transferring ownership...",
    transferOwnershipSuccess: "Ownership successfully transferred",
    transferOwnershipError: "Sorry, we could not transfer ownership to the selected member. Please try again.",
    deleteInviteSubmitLabel: "Delete Invite",
    youBadgeLabel: "You",
    updateOrganizationLoadingMessage: "Updating Organization...",
    updateOrganizationSuccessMessage: "Organization successfully updated",
    updateOrganizationErrorMessage: "Could not update Organization. Please try again.",
    updateLogoErrorMessage: "Could not update Logo. Please try again.",
    organizationNameInputLabel: "Organization Name",
    organizationLogoInputLabel: "Organization Logo",
    updateOrganizationSubmitLabel: "Update Organization",
    inviteMembersPageHeading: "Invite Members",
    goBackToMembersPage: "Go back to members",
    membersPageHeading: "Members",
    inviteMembersButtonLabel: "Invite Members",
    pendingInvitesHeading: "Pending Invites",
    pendingInvitesSubheading: "Manage invites not yet accepted",
    noPendingInvites: "No pending invites found",
    loadingMembers: "Loading members...",
    loadMembersError: "Sorry, we couldn't fetch your organization's members.",
    loadInvitedMembersError: "Sorry, we couldn't fetch your organization's invited members.",
    loadingInvitedMembers: "Loading invited members...",
    invitedBadge: "Invited",
    duplicateInviteEmailError: "You have already entered this email address",
    duplicateInviteNameError: "You have already entered this phone number",
    duplicateInvitePhoneError: "You have already entered this phone number",
    invitingOwnAccountError: "Hey, that's your email!",
    dangerZone: "Danger Zone",
    dangerZoneSubheading: "Delete or leave your organization",
    deleteOrganization: "Delete Organization",
    deleteOrganizationDescription: "This action cannot be undone. All data associated with this organization will be deleted.",
    deletingOrganization: "Deleting organization",
    deleteOrganizationModalHeading: "Deleting Organization",
    deleteOrganizationInputField: "Type the name of the organization to confirm",
    leaveOrganization: "Leave Organization",
    leavingOrganizationModalHeading: "Leaving Organization",
    leaveOrganizationDescription: "You will no longer have access to this organization.",
    deleteOrganizationDisclaimer: "You are deleting the organization {{ organizationName }}. This action cannot be undone.",
    leaveOrganizationDisclaimer: "You are leaving the organization {{ organizationName }}. You will no longer have access to it.",
    deleteOrganizationErrorHeading: "Sorry, we couldn't delete your organization.",
    leaveOrganizationErrorHeading: "Sorry, we couldn't leave your organization."
  }

  export const changePasswordPageText = {
    enterYourNewPasswordLabel: 'Enter Your New Password',
    differentPasswordText: "Your new password must be different from previous passwords.",
    updateYourPassword: "Update Your Password",
    passwordChangedSuccessfully:"Password changed successfully",
  }

  export const employementStatusesText = {
    select: "select",
    internship: "Internship",
    probation: "Probation",
    permanent: "Permanent",

  }

  export const EmployementStatusChangeModalText = {
    employementStatus: "Employement Status",
    submit:"Submit",
    Submitting: "Submitting"
  }

  export const OrganizationText = {

      generalTabLabel: "General",
      generalTabLabelSubheading: "Manage your Organization",
      membersTabLabel: "Members",
      emailSettingsTab: "Email",
      membersTabSubheading: "Manage and Invite members",
      inviteMembersPageSubheading: "Invite members to your organization",
      createOrganizationModalHeading: "Create Organization",
      organizationNameLabel: "Organization Name",
      createOrganizationSubmitLabel: "Create Organization",
      createOrganizationSuccess: "Organization created successfully",
      createOrganizationError: "Organization not created. Please try again.",
      createOrganizationLoading: "Creating organization...",
      settingsPageLabel: "General",
      createOrganizationDropdownLabel: "New organization",
      changeRole: "Change Role",
      removeMember: "Remove",
      inviteMembersSuccess: "Members invited successfully!",
      inviteMembersError: "Sorry, we encountered an error! Please try again",
      inviteMembersLoading: "Inviting members...",
      removeInviteButtonLabel: "Remove invite",
      addAnotherMemberButtonLabel: "Add Another One",
      inviteMembersSubmitLabel: "Send Invites",
      removeMemberModalHeading: "You are removing this user",
      removeMemberSuccessMessage: "Member removed successfully",
      removeMemberErrorMessage: "Sorry, we encountered an error. Please try again",
      removeMemberLoadingMessage: "Removing member...",
      removeMemberSubmitLabel: "Remove User from Organization",
      chooseDifferentRoleError: "Role is the same as the current one",
      updateRoleLoadingMessage: "Updating role...",
      updateRoleSuccessMessage: "Role updated successfully",
      updatingRoleErrorMessage: "Sorry, we encountered an error. Please try again.",
      updateMemberRoleModalHeading: "Update Member's Role",
      memberRoleInputLabel: "Member role",
      updateRoleSubmitLabel: "Update Role",
      transferOwnership: "Transfer Ownership",
      deleteInviteModalHeading: "Deleting Invite",
      deleteInviteSuccessMessage: "Invite deleted successfully",
      deleteInviteErrorMessage: "Invite not deleted. Please try again.",
      deleteInviteLoadingMessage: "Deleting invite. Please wait...",
      confirmDeletingMemberInvite: "You are deleting the invite to <b>{{ email }}</b>",
      transferOwnershipDisclaimer: "You are transferring ownership of the selected organization to <b>{{ member }}</b>. Your new role will be <b>$t(common:roles.admin.label)</b>.",
      transferringOwnership: "Transferring ownership...",
      transferOwnershipSuccess: "Ownership successfully transferred",
      transferOwnershipError: "Sorry, we could not transfer ownership to the selected member. Please try again.",
      deleteInviteSubmitLabel: "Delete Invite",
      youBadgeLabel: "You",
      updateOrganizationLoadingMessage: "Updating Organization...",
      updateOrganizationSuccessMessage: "Organization successfully updated",
      updateOrganizationErrorMessage: "Could not update Organization. Please try again.",
      updateLogoErrorMessage: "Could not update Logo. Please try again.",
      organizationNameInputLabel: "Organization Name",
      organizationLogoInputLabel: "Organization Logo",
      updateOrganizationSubmitLabel: "Update Organization",
      inviteMembersPageHeading: "Invite Members",
      goBackToMembersPage: "Go back to members",
      membersPageHeading: "Members",
      inviteMembersButtonLabel: "Invite Members",
      pendingInvitesHeading: "Pending Invites",
      pendingInvitesSubheading: "Manage invites not yet accepted",
      noPendingInvites: "No pending invites found",
      loadingMembers: "Loading members...",
      loadMembersError: "Sorry, we couldn't fetch your organization's members.",
      loadInvitedMembersError: "Sorry, we couldn't fetch your organization's invited members.",
      loadingInvitedMembers: "Loading invited members...",
      invitedBadge: "Invited",
      duplicateInviteEmailError: "You have already entered this email address",
      duplicateInviteNameError: "You have already entered this phone number",
      duplicateInvitePhoneError: "You have already entered this phone number",
      invitingOwnAccountError: "Hey, that's your email!",
      dangerZone: "Danger Zone",
      dangerZoneSubheading: "Delete or leave your organization",
      deleteOrganization: "Delete Organization",
      deleteOrganizationDescription: "This action cannot be undone. All data associated with this organization will be deleted.",
      deletingOrganization: "Deleting organization",
      deleteOrganizationModalHeading: "Deleting Organization",
      deleteOrganizationInputField: "Type the name of the organization to confirm",
      leaveOrganization: "Leave Organization",
      leavingOrganizationModalHeading: "Leaving Organization",
      leaveOrganizationDescription: "You will no longer have access to this organization.",
      deleteOrganizationDisclaimer: "You are deleting the organization {{ organizationName }}. This action cannot be undone.",
      leaveOrganizationDisclaimer: "You are leaving the organization {{ organizationName }}. You will no longer have access to it.",
      deleteOrganizationErrorHeading: "Sorry, we couldn't delete your organization.",
      leaveOrganizationErrorHeading: "Sorry, we couldn't leave your organization."
  }

  export const leaveCreationFormText={
  applyForLeave:"Apply for leave",
  leave:"Leave",
  casual:"Casual",
  annual:"Annual",
  sick:"Sick",
  leaves:"Leaves",
  quota:"Quota",
  availed:"Availed",
  remaining:"Remaining",
  date:"Date",
  numberOfDays:"Number of Days",
  leaveType:"Leave Type",
  casualLeave:"Casual Leave",
  annualLeave:"Annual Leave",
  sickLeave:"Sick Leave",
  daySlot:'Day Slot',
  daySlotRequired:"Day slot is required",
  reasonForLeave:'Reason for leave',
  submitToManager:"Submit To Manager",
  selectLeaveType:"Select Leave Type"

  }
  export const LeaveTypes= {
    permanent: "permanent",
    probation: "probation",
    internship: "internship"

  }

  export const onboardingText ={
    setupOrganization: "Setup Organization",
    setupOrganizationDescription: "Welcome! First, let's setup your organization.",
    organizationNameLabel: "Organization name",
    errorAlertHeading: "There was an error completing your onboarding.",
    inviteMembers: "Invite members",
    inviteMembersDescription: "Invite your team members to join your organization.",
    info: "Details",
    invites: "Invites",
    complete: "Complete",
    successStepHeading: "You're all set! You can now start using the app.",
    continue: "Continue to your Dashboard",
    settingAccount: "We're setting up your account. Please wait..."
  }

export const teamTimeSheetsText={
  someThingWentWrong:"some thing went wrong",
  dateFormat:"yyyy-MM-dd",
  fetchFailed:"Fetch failed:",
  projectIdNotFoundEmployeeTimesheet:"Project ID not found in the employee timesheet."
}
export const timeSheetsText={
  dateFormat:"yyyy-MM-dd",
  fetchFailed:"Fetch failed:",
  timeSheets:"Time sheets",
  addTimeSheet:"Add TimeSheet",
  filters:"Filters",
  dateRange:"Date Range",
  projects:"Projects",
  allProjects:"All Projects",
  status:"Status",
  all:"All",
  pending:"Pending",
  partialApproved:"Partial Approved",
  approved:"Approved",
  rejected:"Rejected",
  clearFilter:"Clear Filter",
  search :"Search",
  noRecordsFound:"No records found."
}
export const weeklyTimeSheetText ={
  totalTime:"Total Time:",
  pendingTime:" Pending Time:",
  pending:"Pending",
  partialApproved:"Partial Approved",
  approved:"Approved",
  rejected:"Rejected",
  anErrorOccurred:"An error occurred",
  fetchFailed:"Fetch failed:",
  weeklyTimeSheet:"Weekly Timesheets",
  viewAll:"View All",
  date:"Date",
  tickets:"Tickets",
  projects:"Projects",
  hours:"Hours",
  status:"Status",
  actions:"Actions",
  noDataAvailable:"No data available"
}
export const addEditTimeSheetText={
  somethingWentWrong:"Something went wrong.",
  ticketNumberRequiredDevOpsTasks:"Ticket number is required for DevOps tasks.",
  pTags:"<p></p>",
  taskAlreadyAddedProject:"Task already added with this project.",
  projectNotFound:"Project not found",
  date:"Date",
  dateFormat:"YYYY-MM-DD",
  manual:"Manual",
  project:"Project",
  selectTicket:"Select Ticket",
  timeSpent:"Time Spent",
  minutesSpent:"Minutes Spent",
  description:"  Description",
  ticketLinkMustIncluded:"ticket link must be included",
  addToList:"Add to List",
  update: "Update"
}

export const viewTimeSheetText={
  date:"Date:",
  submittedBy:"Submitted By:",
  memberNames:"Member Names",
  designation:"Designation",
  ticketId:"Ticket ID",
  title:"Title",
  timeSpent:"Time Spent",
  manual:"Manual",
  reject:"Reject",
  approve:"Approve"
}
export const projectRecordsText={
  inProgress:"In progress",
  noData:"No Data"
}

export const todayAttendancesText = {
  heading:"Today's Attendance",
  noData:"No Data",
  filterLabels:{
    selectDate:"Select Date",
    status:"status",
  },
  attendanceStatus:{
    presen: "present",
    absent: "absent",
    onLeave:"on leave",
    leavePending: "Pending"
  }
}

export const filterBackgroundText = {
  heading:"Filters",
  clearFilter:"Clear Filter",
  search :"Search",

}

export const SummaryPageText = {
  heading: "Summary",
  filterLabels:{
    employeeName:"Employee Name",
    status:"status",
    selectMonth:"Select Month",
  },
}

export const UpdateShiftTimeText = {
  cancel: "cancel",
  apply: "Apply",
}



